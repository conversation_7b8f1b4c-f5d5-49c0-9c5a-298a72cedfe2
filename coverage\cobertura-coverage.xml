<?xml version="1.0" ?>
<!DOCTYPE coverage SYSTEM "http://cobertura.sourceforge.net/xml/coverage-04.dtd">
<coverage line-rate="0.9327" branch-rate="0.8456" lines-covered="1234" lines-valid="1323" branches-covered="456" branches-valid="539" complexity="0" version="0.1" timestamp="1672531200000">
  <sources>
    <source>src</source>
  </sources>
  <packages>
    <package name="com.example" line-rate="0.9327" branch-rate="0.8456" complexity="0">
      <classes>
        <class name="Example" filename="src/example.js" line-rate="0.9327" branch-rate="0.8456" complexity="0">
          <methods>
            <method name="testMethod" signature="()" line-rate="1.0" branch-rate="1.0">
              <lines>
                <line number="10" hits="5" branch="false"/>
                <line number="11" hits="5" branch="false"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="10" hits="5" branch="false"/>
            <line number="11" hits="5" branch="false"/>
            <line number="15" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>
